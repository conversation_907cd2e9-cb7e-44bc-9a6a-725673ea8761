'use client'
import React, { useEffect, useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import MapDomainModal from './MapDomainModal';
import AskDomainModal from './AskDomainModal';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../components/providers/AuthProvider';
import DocsBotModal from '../../components/modals/DocsBotModal';
import { AlertCircle, Trash2, X, Loader2 } from 'lucide-react';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

interface Site {
  id: string;
  site_name: string;
  domain_name?: string;
  expiry_status: 'Permanent' | 'Temporary';
  expiry_time?: string;
  site_id?: number; // InstaWP site ID for deletion
}

const DashboardPage: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user, session, loading } = useAuth();
  const [sites, setSites] = useState<Site[]>([]);
  const [sitesLoading, setSitesLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState<string>("");
  const [isMapDomainOpen, setIsMapDomainOpen] = useState(false);
  const [isAskDomainOpen, setIsAskDomainOpen] = useState(false);
  const [selectedSiteName, setSelectedSiteName] = useState<string>('');
  const [selectedSiteId, setSelectedSiteId] = useState<string>('');
  const [isDocsBotOpen, setIsDocsBotOpen] = useState(false);
  const [docsBotQuestion, setDocsBotQuestion] = useState<string | null>(null);

  // Delete site state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [siteToDelete, setSiteToDelete] = useState<Site | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Helper function to format expiry date
  const formatExpiryDate = (expiryTime: string | undefined): string => {
    if (!expiryTime) return '';
    try {
      const date = new Date(expiryTime);
      const now = new Date();
      const formattedDate = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
      if (date.getTime() <= now.getTime()) {
        return `Expired ${formattedDate}`;
      }
      return `Expires ${formattedDate}`;
    } catch {
      return '';
    }
  };

  // Helper function to get tooltip text
  const getTooltipText = (site: Site): string => {
    if (!site.expiry_time) return 'No expiry time set';
    const expiry = new Date(site.expiry_time);
    const now = new Date();
    const diffMs = expiry.getTime() - now.getTime();
    if (diffMs <= 0) return 'Expired';
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    return `${diffDays} days ${diffHours} hours left (expires at ${expiry.toLocaleString()})`;
  };

  // Delete site functions
  const handleDeleteSiteClick = (site: Site) => {
    setSiteToDelete(site);
    setIsDeleteModalOpen(true);
    setDeleteError(null);
  };

  const handleConfirmDelete = async () => {
    if (!siteToDelete) return;

    try {
      setDeleteLoading(true);
      setDeleteError(null);

      // Get the current session token with multiple fallback methods
      let currentSession = null;
      let sessionError = null;

      try {
        // Method 1: Use session from AuthProvider if available
        if (session && session.access_token) {
          currentSession = session;
          console.log('Method 1 - Using AuthProvider session:', {
            session: !!currentSession,
            accessToken: 'present',
            user: currentSession.user?.id || 'no user'
          });
        } else {
          // Method 2: Try getSession()
          const sessionResult = await supabase.auth.getSession();
          currentSession = sessionResult.data.session;
          sessionError = sessionResult.error;

          console.log('Method 2 - getSession():', {
            session: !!currentSession,
            sessionError,
            accessToken: currentSession?.access_token ? 'present' : 'missing',
            user: currentSession?.user?.id || 'no user'
          });

          // Method 3: If no session, try getUser() as fallback
          if (!currentSession) {
            console.log('Trying fallback method - getUser()...');
            const userResult = await supabase.auth.getUser();
            if (userResult.data.user && !userResult.error) {
              // Try to refresh the session
              const refreshResult = await supabase.auth.refreshSession();
              currentSession = refreshResult.data.session;
              console.log('Fallback method result:', {
                user: !!userResult.data.user,
                refreshedSession: !!currentSession,
                refreshError: refreshResult.error
              });
            }
          }
        }
      } catch (authError) {
        console.error('Authentication error:', authError);
        sessionError = authError;
      }

      if (sessionError) {
        console.error('Session error:', sessionError);
        throw new Error(`Session error: ${sessionError.message}`);
      }

      if (!currentSession) {
        throw new Error('Not authenticated - no session found. Please try logging out and back in.');
      }

      if (!currentSession.access_token) {
        throw new Error('Not authenticated - no access token. Please try refreshing the page.');
      }

      console.log(`Attempting to delete site: ${siteToDelete.site_name}`);
      console.log(`  - Database ID (UUID): ${siteToDelete.id}`);
      console.log(`  - InstaWP Site ID: ${siteToDelete.site_id}`);
      console.log(`  - User ID: ${currentSession.user?.id}`);
      console.log(`  - Access token length: ${currentSession.access_token.length}`);

      console.log('Making API request to /api/sites/delete...');

      const response = await fetch('/api/sites/delete', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${currentSession.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ site_id: siteToDelete.id }), // Use the UUID id for the API call
      });

      console.log(`API response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
          console.error('API error response:', errorData);
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError);
          errorData = { error: 'Failed to delete site' };
        }

        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Site deletion result:', result);

      // Remove the site from the local state
      setSites(prev => prev.filter(site => site.id !== siteToDelete.id));

      // Close the modal
      setIsDeleteModalOpen(false);
      setSiteToDelete(null);

    } catch (error: any) {
      console.error('Error deleting site:', error);
      setDeleteError(error.message || 'Failed to delete site');
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setSiteToDelete(null);
    setDeleteError(null);
  };

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/login');
    }
  }, [user, loading, router]);

  useEffect(() => {
    // Trigger AskDomainModal if redirected from checkout
    const postCheckout = searchParams.get('postCheckout');
    const siteIdFromParam = searchParams.get('siteId');
    const changePlan = searchParams.get('changePlan');
    if (postCheckout && siteIdFromParam) {
      setSelectedSiteId(siteIdFromParam);
      if (changePlan === '1') {
        // Finalize plan change by calling backend to sync InstaWP
        (async () => {
          try {
            let accessToken: string | null = null;
            if (session?.access_token) {
              accessToken = session.access_token;
            } else {
              const sessionResult = await supabase.auth.getSession();
              accessToken = sessionResult.data.session?.access_token || null;
            }
            if (!accessToken) return;

            const resp = await fetch('/api/plan-change', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ site_id: siteIdFromParam }),
            });
            if (!resp.ok) {
              const err = await resp.json().catch(() => ({ error: 'Plan change failed' }));
              console.error('Plan change finalize failed:', err);
            }
          } catch (e) {
            console.error('Plan change finalize error:', e);
          }
        })();
      } else {
        setIsAskDomainOpen(true);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const { data, error } = await supabase
          .from('user-websites')
          .select('id, site_id, site_name, domain_name, expiry_status, expiry_time');
        if (error) {
          throw error;
        }
        setSites(data as Site[]);
      } catch (err) {
        console.error('Error fetching sites:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setSitesLoading(false);
      }
    };
    fetchSites();
  }, []);

  if (loading || !user) {
    return <div className="py-8 text-center">Checking authentication...</div>;
  }
  if (sitesLoading) {
    return <div className="py-8 text-center">Loading sites...</div>;
  }
  if (error) {
    return <div className="py-8 text-center text-red-500">Error: {error}</div>;
  }

  return (
    <div className="flex flex-col h-full bg-gray-100">
      {/* Dashboard Header */}
      <div className="flex flex-col p-4 mb-4 space-y-4 bg-white rounded-lg shadow-md sm:flex-row sm:items-center sm:justify-between sm:p-6 sm:mb-6 sm:space-y-0">
        <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Websites</h1>
        <div className="flex items-center space-x-4">
          <div className="relative w-full sm:w-auto">
            <input
              type="text"
              placeholder="Search websites..."
              className="w-full py-2 pl-10 pr-4 text-sm border rounded-md sm:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:text-base"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <svg
              className="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 sm:w-5 sm:h-5 left-3 top-1/2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              ></path>
            </svg>
          </div>
        </div>
      </div>

      {/* Sites Table - Desktop */}
      <div className="flex-1 hidden overflow-hidden bg-white rounded-lg shadow-md md:block">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Site Name
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Actions
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6"
              >
                Expiry
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {(search.trim() ? sites.filter(site => site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site) => (
              <tr key={site.id}>
                <td className="flex items-start px-4 py-4 text-sm font-medium text-gray-900 lg:px-6 whitespace-nowrap">
                  <span>{site.domain_name ? site.domain_name : site.site_name}</span>
                </td>
                <td className="px-4 py-4 text-sm font-medium text-right lg:px-6 whitespace-nowrap">
                  <div className="flex items-center justify-end space-x-2">
                    {/* Map Domain Icon - Globe icon, always visible */}
                    <button
                      className="p-1 text-blue-500 hover:text-blue-700"
                      title="Map Domain"
                      onClick={() => { setSelectedSiteName(site.site_name); setIsMapDomainOpen(true); }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                    {/* Go Live Icon - Rocket icon, only for Temporary sites */}
                    {site.expiry_status === 'Temporary' && (
                      <button
                        className="p-1 text-green-500 hover:text-green-700"
                        title="Choose Plan"
                        onClick={() => { window.location.href = `/payments?siteId=${site.id}` }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </button>
                    )}
                    {/* Change Plan Icon - Credit card icon, only for Permanent sites */}
                    {site.expiry_status === 'Permanent' && (
                      <button
                        className="p-1 text-purple-500 hover:text-purple-700"
                        title="Change Plan"
                        onClick={() => { window.location.href = `/payments?siteId=${site.id}&changePlan=1`; }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 lg:w-5 lg:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <rect x="2" y="7" width="20" height="10" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none" />
                          <path d="M2 11h20" stroke="currentColor" strokeWidth="2" />
                          <circle cx="7" cy="15" r="1" fill="currentColor" />
                          <circle cx="11" cy="15" r="1" fill="currentColor" />
                        </svg>
                      </button>
                    )}
                    {/* Delete Site Icon - Trash icon */}
                    <button
                      className="p-1 text-red-500 hover:text-red-700"
                      title="Delete Site"
                      onClick={() => handleDeleteSiteClick(site)}
                    >
                      <Trash2 className="w-4 h-4 lg:w-5 lg:h-5" />
                    </button>
                  </div>
                </td>
                <td className="px-4 py-4 text-sm lg:px-6">
                  {/* Enhanced expiry display for desktop */}
                  {site.expiry_status === 'Temporary' ? (
                    <div className="flex flex-col space-y-1">
                      <span
                        className="inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full w-fit"
                        title={getTooltipText(site)}
                      >
                        Temporary
                      </span>
                      {site.expiry_time && (
                        <span className={`text-xs font-medium ${formatExpiryDate(site.expiry_time).startsWith('Expired') ? 'text-red-500' : 'text-gray-500'}`}>
                          {formatExpiryDate(site.expiry_time)}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full w-fit">
                      Permanent
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {/* Desktop Table Footer */}
        <div className="p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg">
          {sites.length} Sites
        </div>
      </div>

      {/* Mobile Card Layout */}
      <div className="flex-1 space-y-4 md:hidden">
        {(search.trim() ? sites.filter(site => site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site) => (
          <div key={site.id} className="p-4 bg-white rounded-lg shadow-md">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center flex-1 min-w-0">
                <h3 className="text-lg font-medium text-gray-900 truncate">{site.domain_name ? site.domain_name : site.site_name}</h3>
                {site.domain_name && (
                  <div className="text-xs text-blue-600 break-all mt-1">{site.domain_name}</div>
                )}
                <a href="#" className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
              {/* Enhanced expiry badge for mobile */}
              <div className="flex flex-col items-end flex-shrink-0 ml-3 space-y-1">
                {site.expiry_status === 'Temporary' ? (
                  <>
                    <span
                      className="inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full"
                      title={getTooltipText(site)}
                    >
                      Temporary
                    </span>
                    {site.expiry_time && (
                      <span className={`text-xs font-medium whitespace-nowrap ${formatExpiryDate(site.expiry_time).startsWith('Expired') ? 'text-red-500' : 'text-gray-500'}`}>
                        {formatExpiryDate(site.expiry_time)}
                      </span>
                    )}
                  </>
                ) : (
                  <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">
                    Permanent
                  </span>
                )}
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-center justify-start pt-3 space-x-4 border-t border-gray-100">
              {/* Map Domain Button */}
              <button
                className="flex items-center px-3 py-2 space-x-2 text-blue-500 transition-colors rounded-md hover:text-blue-700 hover:bg-blue-50"
                onClick={() => { setSelectedSiteName(site.site_name); setIsMapDomainOpen(true); }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Map Domain</span>
              </button>

              {/* Go Live Button - only for Temporary sites */}
              {site.expiry_status === 'Temporary' && (
                <button
                  className="flex items-center px-3 py-2 space-x-2 text-green-500 transition-colors rounded-md hover:text-green-700 hover:bg-green-50"
                  onClick={() => { window.location.href = `/payments?siteId=${site.id}` }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span className="text-sm font-medium">Go Live</span>
                </button>
              )}

              {/* Change Plan Button - only for Permanent sites */}
              {site.expiry_status === 'Permanent' && (
                <button
                  className="flex items-center px-3 py-2 space-x-2 text-purple-500 transition-colors rounded-md hover:text-purple-700 hover:bg-purple-50"
                  onClick={() => { window.location.href = `/payments?siteId=${site.id}&changePlan=1`; }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <rect x="2" y="7" width="20" height="10" rx="2" ry="2" stroke="currentColor" strokeWidth="2" fill="none" />
                    <path d="M2 11h20" stroke="currentColor" strokeWidth="2" />
                    <circle cx="7" cy="15" r="1" fill="currentColor" />
                    <circle cx="11" cy="15" r="1" fill="currentColor" />
                  </svg>
                  <span className="text-sm font-medium">Change Plan</span>
                </button>
              )}

              {/* Delete Site Button */}
              <button
                className="flex items-center px-3 py-2 space-x-2 text-red-500 transition-colors rounded-md hover:text-red-700 hover:bg-red-50"
                onClick={() => handleDeleteSiteClick(site)}
              >
                <Trash2 className="w-5 h-5" />
                <span className="text-sm font-medium">Delete Site</span>
              </button>
            </div>
          </div>
        ))}

        {/* Mobile Footer */}
        <div className="p-4 text-sm text-center text-gray-600 bg-white rounded-lg shadow-md">
          {sites.length} Sites
        </div>
      </div>
      <MapDomainModal
        isOpen={isMapDomainOpen}
        onClose={() => setIsMapDomainOpen(false)}
        siteName={selectedSiteName}
        onHelp={() => {
          setDocsBotQuestion('how is map domain done');
          setIsDocsBotOpen(true);
        }}
      />

      <AskDomainModal
        isOpen={isAskDomainOpen}
        onYes={() => {
          // open map domain modal
          const site = sites.find(s => s.id === selectedSiteId);
          if (site) {
            setSelectedSiteName(site.site_name);
            setIsMapDomainOpen(true);
          }
          setIsAskDomainOpen(false);
        }}
        onNo={() => {
          router.push(`/dashboard/domain`);
          setIsAskDomainOpen(false);
        }}
      />

      <DocsBotModal
        isOpen={isDocsBotOpen}
        onClose={() => {
          setIsDocsBotOpen(false);
          setDocsBotQuestion(null);
        }}
        question={docsBotQuestion}
      />

      {/* Delete Site Confirmation Modal */}
      {isDeleteModalOpen && siteToDelete && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={handleCancelDelete}
          />

          {/* Modal */}
          <div className="relative w-full max-w-md p-6 mx-4 bg-white rounded-lg shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Delete Site</h3>
              <button
                onClick={handleCancelDelete}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                Are you sure you want to delete <strong>{siteToDelete.site_name}</strong>?
              </p>
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="text-sm text-red-800">
                    <p className="font-medium mb-1">This action cannot be undone.</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Your website will be permanently deleted from InstaWP</li>
                      <li>Your subscription will be canceled immediately</li>
                      <li>All website data will be lost</li>
                    </ul>
                  </div>
                </div>
              </div>

              {deleteError && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-800">{deleteError}</p>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <button
                onClick={handleCancelDelete}
                disabled={deleteLoading}
                className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={deleteLoading}
                className="flex-1 px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 flex items-center justify-center"
              >
                {deleteLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  'Delete Site'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;