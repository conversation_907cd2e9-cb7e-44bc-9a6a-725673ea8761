import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

// Create a function to get authenticated Supabase client
function createAuthenticatedSupabaseClient(accessToken: string) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    }
  );
}

// DELETE /api/sites/delete - Delete a site and cancel its subscription
export async function DELETE(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Create authenticated Supabase client with the user's token
    const supabase = createAuthenticatedSupabaseClient(token);

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // Get site_id from request body (this is the UUID id from the database)
    const { site_id } = await req.json();

    if (!site_id) {
      return NextResponse.json({ error: 'Site ID is required' }, { status: 400 });
    }

    console.log(`Delete request for database site_id: ${site_id} by user: ${user.id} (${user.email})`);

    // First, let's check what sites exist for this user to debug the ID mismatch
    const { data: allUserSites } = await supabase
      .from('user-websites')
      .select('id, site_id, site_name')
      .eq('user_id', user.id);

    console.log(`User ${user.id} has ${allUserSites?.length || 0} sites:`);
    if (allUserSites) {
      allUserSites.forEach((s, i) => {
        console.log(`  ${i + 1}. ID: ${s.id}, site_id: ${s.site_id}, name: ${s.site_name}`);
      });
    }

    // Verify the user owns this site and get both the UUID id and the InstaWP site_id
    let { data: site, error: siteError } = await supabase
      .from('user-websites')
      .select('id, site_id, site_name, created_at')
      .eq('id', site_id)
      .eq('user_id', user.id)
      .single();

    if (siteError || !site) {
      console.error(`Site not found or access denied.`);
      console.error(`  - Requested site_id: ${site_id}`);
      console.error(`  - User ID: ${user.id}`);
      console.error(`  - Error:`, siteError);

      // Try to find the site by site_id (integer) instead of id (UUID) as a fallback
      console.log(`Attempting fallback: searching by site_id field...`);
      const { data: fallbackSite, error: fallbackError } = await supabase
        .from('user-websites')
        .select('id, site_id, site_name, created_at')
        .eq('site_id', site_id)
        .eq('user_id', user.id)
        .single();

      if (fallbackSite) {
        console.log(`Found site by site_id field: ${fallbackSite.site_name} (UUID: ${fallbackSite.id})`);
        // Use the fallback site
        site = fallbackSite;
        siteError = null;
      } else {
        console.error(`Fallback also failed:`, fallbackError);
        return NextResponse.json({
          error: 'Site not found or access denied',
          debug: {
            requested_id: site_id,
            user_id: user.id,
            available_sites: allUserSites?.map(s => ({ id: s.id, site_id: s.site_id, name: s.site_name }))
          }
        }, { status: 404 });
      }
    }

    console.log(`Verified site ownership: ${site.site_name}`);
    console.log(`  - Database ID (UUID): ${site.id}`);
    console.log(`  - InstaWP Site ID: ${site.site_id}`);
    console.log(`  - Belongs to user: ${user.id}`);

    const results = {
      site_deleted: false,
      subscription_canceled: false,
      database_cleaned: false,
      errors: [] as string[]
    };

    // Step 1: Find and cancel associated subscription
    try {
      const stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);

      if (stripeCustomerId) {
        console.log(`Looking for subscriptions for customer ${stripeCustomerId} and site ${site.id} (InstaWP ID: ${site.site_id})`);

        // Find all subscriptions for this customer (both active and inactive)
        const subscriptions = await stripe.subscriptions.list({
          customer: stripeCustomerId,
          limit: 100,
          expand: ['data.items.data.price']
        });

        console.log(`Found ${subscriptions.data.length} total subscriptions for customer`);

        // Find subscription associated with this site using the database UUID id (which should be in metadata.siteId)
        let siteSubscription = subscriptions.data.find(sub => {
          const metadataSiteId = sub.metadata?.siteId;
          console.log(`Checking subscription ${sub.id}: metadata.siteId = ${metadataSiteId}, looking for ${site.id}`);
          return metadataSiteId === site.id; // Use the UUID id for subscription matching
        });

        // Fallback: If no subscription found with metadata, try to find by other means
        if (!siteSubscription && subscriptions.data.length > 0) {
          console.log(`No subscription found with metadata.siteId = ${site_id}, trying fallback methods...`);

          // Get site creation date for comparison
          const siteCreatedAt = new Date(site.created_at);

          // Find active subscriptions created around the same time as the site
          const candidateSubscriptions = subscriptions.data.filter(sub => {
            const subCreatedAt = new Date(sub.created * 1000);
            const timeDiff = Math.abs(subCreatedAt.getTime() - siteCreatedAt.getTime());
            const hoursDiff = timeDiff / (1000 * 60 * 60);

            console.log(`Subscription ${sub.id}: created ${subCreatedAt.toISOString()}, site created ${siteCreatedAt.toISOString()}, diff: ${hoursDiff.toFixed(2)} hours`);

            // Consider subscriptions created within 24 hours of the site
            return hoursDiff <= 24 && sub.status !== 'canceled';
          });

          if (candidateSubscriptions.length === 1) {
            siteSubscription = candidateSubscriptions[0];
            console.log(`Found candidate subscription ${siteSubscription.id} based on creation time`);
          } else if (candidateSubscriptions.length > 1) {
            console.log(`Multiple candidate subscriptions found (${candidateSubscriptions.length}), using the most recent one`);
            siteSubscription = candidateSubscriptions.sort((a, b) => b.created - a.created)[0];
            console.log(`Selected subscription ${siteSubscription.id} as the most recent candidate`);
          } else {
            console.log(`No candidate subscriptions found within 24 hours of site creation`);
          }
        }

        if (siteSubscription) {
          console.log(`Found subscription ${siteSubscription.id} for site ${site.site_name} (DB ID: ${site.id}), status: ${siteSubscription.status}`);

          // Safety check: Confirm this subscription belongs to the correct customer
          if (siteSubscription.customer === stripeCustomerId) {
            // Only cancel if not already canceled
            if (siteSubscription.status !== 'canceled') {
              await stripe.subscriptions.cancel(siteSubscription.id);
              console.log(`Successfully canceled subscription ${siteSubscription.id} for site ${site.site_name}`);
            } else {
              console.log(`Subscription ${siteSubscription.id} was already canceled`);
            }
            results.subscription_canceled = true;
          } else {
            console.error(`Safety check failed: Subscription ${siteSubscription.id} belongs to customer ${siteSubscription.customer}, but expected ${stripeCustomerId}`);
            results.errors.push(`Subscription safety check failed - customer mismatch`);
          }
        } else {
          console.log(`No subscription found for site ${site_id}. Available subscriptions:`);
          subscriptions.data.forEach(sub => {
            console.log(`  - Subscription ${sub.id}: metadata.siteId = ${sub.metadata?.siteId}, status = ${sub.status}`);
          });
          results.subscription_canceled = true; // Not an error if no subscription exists
        }
      } else {
        console.log(`No Stripe customer found for user ${user.email}`);
        results.subscription_canceled = true; // Not an error if no Stripe customer
      }
    } catch (error: any) {
      console.error('Error canceling subscription:', error);
      results.errors.push(`Subscription cancellation failed: ${error.message}`);
    }

    // Step 2: Delete site from InstaWP
    try {
      const instawpToken = process.env.INSTAWP_API_TOKEN;
      const instawpBaseUrl = process.env.INSTAWP_API_BASE_URL || 'https://app.instawp.io/api/v2';

      console.log(`InstaWP deletion attempt:`);
      console.log(`  - Token configured: ${!!instawpToken}`);
      console.log(`  - Base URL: ${instawpBaseUrl}`);
      console.log(`  - InstaWP Site ID: ${site.site_id}`);
      console.log(`  - Full URL: ${instawpBaseUrl}/sites/${site.site_id}`);

      if (instawpToken) {
        console.log(`Making DELETE request to InstaWP API...`);

        const instawpResponse = await fetch(`${instawpBaseUrl}/sites/${site.site_id}`, {
          method: 'DELETE',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${instawpToken}`,
            'Content-Type': 'application/json',
          },
        });

        console.log(`InstaWP API Response:`);
        console.log(`  - Status: ${instawpResponse.status} ${instawpResponse.statusText}`);
        console.log(`  - Headers:`, Object.fromEntries(instawpResponse.headers.entries()));

        const responseText = await instawpResponse.text();
        console.log(`  - Response body: ${responseText}`);

        if (instawpResponse.ok || instawpResponse.status === 404) {
          results.site_deleted = true;
          console.log(`✓ Site ${site.site_id} (${site.site_name}) deletion request sent to InstaWP successfully`);
        } else {
          throw new Error(`InstaWP API error (${instawpResponse.status}): ${responseText}`);
        }
      } else {
        console.warn('InstaWP API token not configured, skipping InstaWP deletion');
        results.site_deleted = true; // Don't fail if InstaWP is not configured
      }
    } catch (error: any) {
      console.error('Error deleting site from InstaWP:', error);
      results.errors.push(`InstaWP deletion failed: ${error.message}`);
    }

    // Step 3: Remove site from database
    try {
      console.log(`Database deletion attempt:`);
      console.log(`  - Database ID (UUID): ${site.id}`);
      console.log(`  - InstaWP Site ID: ${site.site_id}`);
      console.log(`  - User ID: ${user.id}`);

      const { data: deletedData, error: deleteError } = await supabase
        .from('user-websites')
        .delete()
        .eq('id', site.id) // Use the UUID id for database operations
        .eq('user_id', user.id)
        .select(); // Add select to see what was deleted

      console.log(`Database deletion result:`);
      console.log(`  - Error: ${deleteError ? JSON.stringify(deleteError) : 'None'}`);
      console.log(`  - Deleted rows: ${deletedData ? deletedData.length : 0}`);
      console.log(`  - Deleted data:`, deletedData);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      if (!deletedData || deletedData.length === 0) {
        throw new Error(`No rows were deleted. Site ${site.id} may not exist or belong to user ${user.id}`);
      }

      results.database_cleaned = true;
      console.log(`✓ Site ${site.site_name} (DB ID: ${site.id}) removed from database successfully`);
    } catch (error: any) {
      console.error('Error deleting site from database:', error);
      results.errors.push(`Database cleanup failed: ${error.message}`);
    }

    // Determine overall success
    const success = results.database_cleaned && (results.errors.length === 0);

    if (success) {
      return NextResponse.json({
        message: 'Site deleted successfully',
        database_id: site.id,
        instawp_site_id: site.site_id,
        site_name: site.site_name,
        details: results
      });
    } else {
      return NextResponse.json({
        error: 'Site deletion completed with errors',
        database_id: site.id,
        instawp_site_id: site.site_id,
        site_name: site.site_name,
        details: results
      }, { status: 207 }); // 207 Multi-Status for partial success
    }

  } catch (error: any) {
    console.error('Site deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
