import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a function to get authenticated Supabase client
function createAuthenticatedSupabaseClient(accessToken: string) {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      global: {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    }
  );
}

// GET /api/debug/sites - Debug endpoint to check sites in database
export async function GET(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Create authenticated Supabase client with the user's token
    const supabase = createAuthenticatedSupabaseClient(token);

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    console.log(`Debug sites request from user: ${user.id} (${user.email})`);

    // Fetch all sites for this user
    const { data: sites, error: sitesError } = await supabase
      .from('user-websites')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (sitesError) {
      console.error('Error fetching sites:', sitesError);
      return NextResponse.json({ error: 'Failed to fetch sites' }, { status: 500 });
    }

    console.log(`Found ${sites?.length || 0} sites for user ${user.id}`);
    
    if (sites) {
      sites.forEach((site, index) => {
        console.log(`Site ${index + 1}:`);
        console.log(`  - Database ID (UUID): ${site.id}`);
        console.log(`  - InstaWP Site ID: ${site.site_id}`);
        console.log(`  - Name: ${site.site_name}`);
        console.log(`  - Created: ${site.created_at}`);
        console.log(`  - Updated: ${site.updated_at}`);
      });
    }

    return NextResponse.json({ 
      user_id: user.id,
      user_email: user.email,
      sites: sites || [],
      total_sites: sites?.length || 0
    });

  } catch (error: any) {
    console.error('Debug sites error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
