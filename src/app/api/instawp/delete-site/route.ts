import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// DELETE /api/instawp/delete-site - Delete a site via InstaWP API
export async function DELETE(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // Get site_id from request body
    const { site_id } = await req.json();
    
    if (!site_id) {
      return NextResponse.json({ error: 'Site ID is required' }, { status: 400 });
    }

    // Verify the user owns this site
    const { data: site, error: siteError } = await supabase
      .from('user-websites')
      .select('id, site_name')
      .eq('id', site_id)
      .eq('user_id', user.id)
      .single();

    if (siteError || !site) {
      return NextResponse.json({ error: 'Site not found or access denied' }, { status: 404 });
    }

    // Get InstaWP API configuration
    const instawpToken = process.env.INSTAWP_API_TOKEN;
    const instawpBaseUrl = process.env.INSTAWP_API_BASE_URL || 'https://app.instawp.io/api/v2';

    if (!instawpToken) {
      console.error('InstaWP API token not configured');
      return NextResponse.json({ error: 'InstaWP API not configured' }, { status: 500 });
    }

    // Call InstaWP API to delete the site
    const instawpResponse = await fetch(`${instawpBaseUrl}/sites/${site_id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${instawpToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!instawpResponse.ok) {
      const errorText = await instawpResponse.text();
      console.error('InstaWP deletion error:', instawpResponse.status, errorText);
      
      // If site not found on InstaWP, we can still proceed with local cleanup
      if (instawpResponse.status === 404) {
        console.warn(`Site ${site_id} not found on InstaWP, proceeding with local cleanup`);
      } else {
        return NextResponse.json({ 
          error: `Failed to delete site from InstaWP: ${errorText}` 
        }, { status: instawpResponse.status });
      }
    }

    // Remove site from local database
    const { error: deleteError } = await supabase
      .from('user-websites')
      .delete()
      .eq('id', site_id)
      .eq('user_id', user.id);

    if (deleteError) {
      console.error('Error deleting site from database:', deleteError);
      return NextResponse.json({ error: 'Failed to delete site from database' }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'Site deleted successfully',
      site_id,
      site_name: site.site_name
    });

  } catch (error: any) {
    console.error('Site deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
