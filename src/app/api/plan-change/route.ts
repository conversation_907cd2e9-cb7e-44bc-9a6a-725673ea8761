import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { stripe } from '@/lib/stripe';
import { getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';
import { getPriceId, plans as localPlans } from '@/lib/stripe-plans';

// POST /api/plan-change - Update Stripe subscription and upgrade InstaWP site plan
export async function POST(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Authenticated supabase client with user JWT so R<PERSON> applies
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      { global: { headers: { Authorization: `<PERSON><PERSON> ${token}` } } }
    );

    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const { site_id, plan_id, is_yearly } = await req.json();
    if (!site_id) {
      return NextResponse.json({ error: 'site_id is required' }, { status: 400 });
    }

    // Get site by UUID id, verify ownership and get InstaWP site_id
    const { data: site, error: siteError } = await supabase
      .from('user-websites')
      .select('id, site_id, site_name, user_id, created_at')
      .eq('id', site_id)
      .eq('user_id', user.id)
      .single();

    if (siteError || !site) {
      return NextResponse.json({ error: 'Site not found or access denied' }, { status: 404 });
    }

    // Find user's Stripe customer
    const stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);
    if (!stripeCustomerId) {
      return NextResponse.json({ error: 'Stripe customer not found' }, { status: 404 });
    }

    // Find subscription for this site using metadata.siteId
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      limit: 100,
      expand: ['data.items.data.price'],
    });

    let sub = subscriptions.data.find(s => s.metadata?.siteId === site.id && s.status !== 'canceled');

    // Fallback: If no subscription found with metadata, try to find by creation time proximity
    if (!sub && subscriptions.data.length > 0) {
      const siteCreatedAt = new Date(site.created_at);
      const candidateSubscriptions = subscriptions.data.filter(s => {
        const subCreatedAt = new Date(s.created * 1000);
        const hoursDiff = Math.abs(subCreatedAt.getTime() - siteCreatedAt.getTime()) / (1000 * 60 * 60);
        return hoursDiff <= 24 && s.status !== 'canceled';
      });
      if (candidateSubscriptions.length === 1) {
        sub = candidateSubscriptions[0];
      } else if (candidateSubscriptions.length > 1) {
        // Use most recent
        sub = candidateSubscriptions.sort((a, b) => b.created - a.created)[0];
      }
    }

    if (!sub) {
      return NextResponse.json({ error: 'Active subscription for site not found' }, { status: 404 });
    }

    // Prepare effective plan id: from input or inferred from subscription price
    let effectivePlanId: string | null = plan_id || null;

    if (!effectivePlanId) {
      const currentPriceId = sub.items.data[0]?.price?.id as string | undefined;
      if (currentPriceId) {
        // Reverse map from localPlans
        for (const [pid, mapping] of Object.entries(localPlans)) {
          if (mapping.monthly === currentPriceId || mapping.yearly === currentPriceId) {
            effectivePlanId = pid;
            break;
          }
        }
      }
    }

    if (!effectivePlanId) {
      return NextResponse.json({ error: 'Unable to determine plan from subscription' }, { status: 400 });
    }

    // If plan_id provided, update Stripe subscription to new price; else assume already updated by Checkout
    let updatedSubscriptionId = sub.id;
    if (plan_id) {
      // Determine new Stripe price ID
      let newPriceId: string;
      try {
        newPriceId = getPriceId(plan_id, !!is_yearly);
      } catch (e) {
        return NextResponse.json({ error: 'Invalid plan_id' }, { status: 400 });
      }

      const currentItem = sub.items.data[0];
      const updatedSub = await stripe.subscriptions.update(sub.id, {
        items: [
          {
            id: currentItem.id,
            price: newPriceId,
            quantity: 1,
          },
        ],
        proration_behavior: 'create_prorations',
        metadata: { ...sub.metadata, planId: plan_id },
      });
      updatedSubscriptionId = updatedSub.id;
    }

    // Retrieve InstaWP plan id from plans table via instawp_plan_id mapping
    const { data: planRow, error: planError } = await supabase
      .from('plans')
      .select('plan_id, instawp_plan_id')
      .eq('plan_id', effectivePlanId)
      .single();

    if (planError || !planRow?.instawp_plan_id) {
      return NextResponse.json({ error: 'InstaWP mapping not found for plan' }, { status: 400 });
    }

    const instawpToken = process.env.INSTAWP_API_TOKEN;
    const instawpBaseUrl = process.env.INSTAWP_API_BASE_URL || 'https://app.instawp.io/api/v2';
    if (!instawpToken) {
      console.warn('InstaWP token missing; skipping InstaWP upgrade');
      return NextResponse.json({ message: 'Subscription updated, InstaWP not configured' });
    }

    // Call InstaWP upgrade-plan endpoint
    const upgradeResp = await fetch(`${instawpBaseUrl}/sites/${site.site_id}/upgrade-plan`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${instawpToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ plan_id: planRow.instawp_plan_id }),
    });

    // Read response once and log it fully (status + body)
    const upgradeRawText = await upgradeResp.text();
    console.log('InstaWP upgrade-plan response', {
      status: upgradeResp.status,
      ok: upgradeResp.ok,
      siteId: site.site_id,
      instawpPlanId: planRow.instawp_plan_id,
      body: upgradeRawText,
    });

    if (!upgradeResp.ok) {
      console.error('InstaWP upgrade failed:', upgradeResp.status, upgradeRawText);
      return NextResponse.json({ error: `InstaWP upgrade failed: ${upgradeRawText}` }, { status: 502 });
    }

    // Safely parse JSON if possible; fall back to empty object
    let upgradeJson: any = {};
    try {
      upgradeJson = upgradeRawText ? JSON.parse(upgradeRawText) : {};
    } catch {
      upgradeJson = {};
    }

    return NextResponse.json({
      message: 'Plan changed successfully',
      stripe_subscription_id: updatedSubscriptionId,
      plan_id: effectivePlanId,
      instawp: upgradeJson,
    });
  } catch (error: any) {
    console.error('Plan change error:', error);
    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json({ error: `Stripe error: ${error.message}` }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 