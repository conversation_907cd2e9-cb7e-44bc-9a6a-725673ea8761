import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// POST /api/stripe/cancel-subscription - Cancel a Stripe subscription
export async function POST(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // Get subscription_id from request body
    const { subscription_id, site_id } = await req.json();
    
    if (!subscription_id) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Get the Stripe customer ID to verify ownership
    const stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);
    
    if (!stripeCustomerId) {
      return NextResponse.json({ error: 'No Stripe customer found' }, { status: 404 });
    }

    // Retrieve the subscription to verify ownership
    const subscription = await stripe.subscriptions.retrieve(subscription_id);
    
    if (subscription.customer !== stripeCustomerId) {
      return NextResponse.json({ error: 'Subscription not found or access denied' }, { status: 404 });
    }

    // Cancel the subscription immediately
    const canceledSubscription = await stripe.subscriptions.cancel(subscription_id);

    // Log the cancellation
    console.log(`Subscription ${subscription_id} canceled for user ${user.id}`, {
      site_id,
      subscription_status: canceledSubscription.status,
      canceled_at: canceledSubscription.canceled_at
    });

    return NextResponse.json({ 
      message: 'Subscription canceled successfully',
      subscription_id,
      status: canceledSubscription.status,
      canceled_at: canceledSubscription.canceled_at
    });

  } catch (error: any) {
    console.error('Subscription cancellation error:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json({ 
        error: `Stripe error: ${error.message}` 
      }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
