import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// POST /api/stripe/update-payment-method - Update default payment method for subscriptions
export async function POST(req: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // Get payment_method_id from request body
    const { payment_method_id } = await req.json();
    
    if (!payment_method_id) {
      return NextResponse.json({ error: 'Payment method ID is required' }, { status: 400 });
    }

    // Get the Stripe customer ID
    const stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);
    
    if (!stripeCustomerId) {
      return NextResponse.json({ error: 'No Stripe customer found' }, { status: 404 });
    }

    // Verify the payment method belongs to this customer
    const paymentMethod = await stripe.paymentMethods.retrieve(payment_method_id);
    
    if (paymentMethod.customer !== stripeCustomerId) {
      return NextResponse.json({ error: 'Payment method not found or access denied' }, { status: 404 });
    }

    // Update the customer's default payment method
    await stripe.customers.update(stripeCustomerId, {
      invoice_settings: {
        default_payment_method: payment_method_id,
      },
    });

    // Update all active subscriptions to use this payment method
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      status: 'active',
    });

    const updatePromises = subscriptions.data.map(subscription =>
      stripe.subscriptions.update(subscription.id, {
        default_payment_method: payment_method_id,
      })
    );

    await Promise.all(updatePromises);

    // Get updated payment method details
    const updatedPaymentMethod = await stripe.paymentMethods.retrieve(payment_method_id);

    return NextResponse.json({ 
      message: 'Payment method updated successfully',
      payment_method: {
        id: updatedPaymentMethod.id,
        type: updatedPaymentMethod.type,
        last4: updatedPaymentMethod.card?.last4,
        brand: updatedPaymentMethod.card?.brand,
        exp_month: updatedPaymentMethod.card?.exp_month,
        exp_year: updatedPaymentMethod.card?.exp_year,
      },
      subscriptions_updated: subscriptions.data.length
    });

  } catch (error: any) {
    console.error('Payment method update error:', error);
    
    // Handle specific Stripe errors
    if (error.type === 'StripeInvalidRequestError') {
      return NextResponse.json({ 
        error: `Stripe error: ${error.message}` 
      }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
